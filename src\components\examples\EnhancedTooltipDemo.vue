<template>
  <div class="p-6">
    <h2 class="text-2xl font-bold mb-6">增强型Tooltip演示</h2>
    
    <!-- 断面图表示例 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4">断面监控图表（带计划差值和市场差值）</h3>
      <LineChart
        title="断面曲线"
        :data="sectionData"
        height="300px"
        :custom-config="sectionConfig"
      />
    </div>

    <!-- 负荷预测示例 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4">负荷预测图表（带预测误差和误差率）</h3>
      <LineChart
        title="负荷预测"
        :data="loadData"
        height="300px"
        :custom-config="loadConfig"
      />
    </div>

    <!-- 新能源出力示例 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4">新能源出力分析（带弃电量和利用率）</h3>
      <LineChart
        title="新能源出力"
        :data="renewableData"
        height="300px"
        :custom-config="renewableConfig"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LineChart from '@/components/shared/charts/LineChart.vue'
import { createEnhancedTooltipFormatter, createSectionChartTooltipConfig } from '@/utils/chart'
import type { SeriesData } from '@/types'

// 生成模拟数据
const generateMockData = (baseValue: number, variance: number = 20, count: number = 24) => {
  return Array.from({ length: count }, (_, i) => ({
    name: `${String(i).padStart(2, '0')}:00`,
    value: baseValue + (Math.random() - 0.5) * variance
  }))
}

// 断面图表数据
const sectionData = ref<SeriesData[]>([
  {
    name: '实时计划',
    color: '#3075F6',
    data: generateMockData(120, 30),
  },
  {
    name: '实时市场',
    color: '#FFB637',
    data: generateMockData(110, 25),
  },
  {
    name: '实时潮流',
    color: '#34C38F',
    data: generateMockData(100, 20),
  },
  {
    name: '断面限额',
    color: '#999999',
    data: Array.from({ length: 24 }, () => ({ name: '', value: 150 })),
  },
])

// 负荷预测数据
const loadData = ref<SeriesData[]>([
  {
    name: '实际负荷',
    color: '#E74C3C',
    data: generateMockData(800, 100),
  },
  {
    name: '预测负荷',
    color: '#3498DB',
    data: generateMockData(820, 80),
  },
])

// 新能源出力数据
const renewableData = ref<SeriesData[]>([
  {
    name: '预测出力',
    color: '#F39C12',
    data: generateMockData(200, 50),
  },
  {
    name: '实际出力',
    color: '#27AE60',
    data: generateMockData(180, 40),
  },
])

// 断面图表配置
const sectionConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter(createSectionChartTooltipConfig())
  }
}

// 负荷预测图表配置
const loadConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter({
      calculatedFields: [
        {
          name: '预测误差',
          calculate: (values) => values['预测负荷'] - values['实际负荷'],
          color: '#F39C12'
        },
        {
          name: '误差率',
          calculate: (values) => {
            const actual = values['实际负荷']
            const forecast = values['预测负荷']
            return actual !== 0 ? ((forecast - actual) / actual) * 100 : 0
          },
          color: '#9B59B6',
          unit: '%',
          formatter: (value) => value.toFixed(1)
        }
      ]
    })
  }
}

// 新能源出力图表配置
const renewableConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter({
      calculatedFields: [
        {
          name: '弃电量',
          calculate: (values) => Math.max(0, values['预测出力'] - values['实际出力']),
          color: '#E74C3C'
        },
        {
          name: '利用率',
          calculate: (values) => {
            const forecast = values['预测出力']
            const actual = values['实际出力']
            return forecast !== 0 ? (actual / forecast) * 100 : 0
          },
          color: '#27AE60',
          unit: '%',
          formatter: (value) => value.toFixed(1)
        }
      ],
      valueFormatter: (value) => value.toFixed(1)
    })
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>
