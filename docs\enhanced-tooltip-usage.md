# 增强型图表Tooltip使用指南

## 概述

增强型tooltip工具提供了一个通用的解决方案，用于在图表悬浮窗中显示计算字段，如差值、比率等。

## 核心功能

### 1. 基础功能
- 显示原有数据系列
- 支持自定义计算字段
- 自动格式化数值
- 支持正负值颜色区分
- 可配置分隔线和样式

### 2. 计算字段支持
- 支持任意数量的计算字段
- 每个字段可自定义计算逻辑
- 支持自定义颜色和单位
- 支持自定义格式化函数

## 使用方法

### 基础用法

```typescript
import { createEnhancedTooltipFormatter } from '@/utils/chart/tooltip'

// 在图表配置中使用
const chartConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter({
      calculatedFields: [
        {
          name: '差值',
          calculate: (values) => values['系列A'] - values['系列B'],
          color: '#E74C3C',
        }
      ]
    })
  }
}
```

### 断面图表专用配置

```typescript
import { createEnhancedTooltipFormatter, createSectionChartTooltipConfig } from '@/utils/chart/tooltip'

// 使用预定义的断面图表配置
const chartConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter(createSectionChartTooltipConfig())
  }
}
```

### 自定义计算字段

```typescript
import { createEnhancedTooltipFormatter, COMMON_CALCULATED_FIELDS } from '@/utils/chart/tooltip'

const chartConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter({
      calculatedFields: [
        // 使用预定义字段
        COMMON_CALCULATED_FIELDS.PLAN_DIFF,
        COMMON_CALCULATED_FIELDS.MARKET_DIFF,
        // 自定义字段
        {
          name: '利用率',
          calculate: (values) => (values['实际值'] / values['计划值']) * 100,
          color: '#3498DB',
          unit: '%',
          formatter: (value) => value.toFixed(1)
        }
      ],
      showSeparator: true,
      valueFormatter: (value) => value.toFixed(3)
    })
  }
}
```

## 配置选项

### TooltipOptions

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| calculatedFields | CalculatedField[] | [] | 计算字段配置数组 |
| showSeparator | boolean | true | 是否显示分隔线 |
| valueFormatter | function | (v) => v.toFixed(2) | 数值格式化函数 |
| customStyle | string | '' | 自定义CSS样式 |

### CalculatedField

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | string | - | 字段显示名称 |
| calculate | function | - | 计算函数，接收values对象 |
| color | string | '#666666' | 显示颜色 |
| unit | string | 'MW' | 单位 |
| formatter | function | - | 自定义格式化函数 |

## 预定义计算字段

### COMMON_CALCULATED_FIELDS

- `PLAN_DIFF`: 计划差值（实时计划 - 实时潮流）
- `MARKET_DIFF`: 市场差值（实时市场 - 实时潮流）
- `PLAN_MARKET_DIFF`: 计划市场差值（实时计划 - 实时市场）

## 实际应用示例

### 1. 断面监控图表

```typescript
// 在 useSectionChart.ts 中
const customConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter(createSectionChartTooltipConfig())
  }
}
```

### 2. 负荷预测图表

```typescript
const loadForecastConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter({
      calculatedFields: [
        {
          name: '预测误差',
          calculate: (values) => values['预测值'] - values['实际值'],
          color: '#F39C12'
        },
        {
          name: '误差率',
          calculate: (values) => {
            const actual = values['实际值']
            const forecast = values['预测值']
            return actual !== 0 ? ((forecast - actual) / actual) * 100 : 0
          },
          color: '#9B59B6',
          unit: '%',
          formatter: (value) => value.toFixed(1)
        }
      ]
    })
  }
}
```

### 3. 新能源出力分析

```typescript
const renewableConfig = {
  tooltip: {
    formatter: createEnhancedTooltipFormatter({
      calculatedFields: [
        {
          name: '弃电量',
          calculate: (values) => Math.max(0, values['预测出力'] - values['实际出力']),
          color: '#E74C3C'
        },
        {
          name: '利用率',
          calculate: (values) => {
            const forecast = values['预测出力']
            const actual = values['实际出力']
            return forecast !== 0 ? (actual / forecast) * 100 : 0
          },
          color: '#27AE60',
          unit: '%'
        }
      ],
      valueFormatter: (value) => value.toFixed(1)
    })
  }
}
```

## 注意事项

1. **系列名称匹配**: 计算函数中使用的系列名称必须与图表数据中的name字段完全匹配
2. **数据类型**: 确保数据值可以转换为数字类型
3. **性能考虑**: 避免在计算函数中进行复杂的异步操作
4. **错误处理**: 计算函数应该处理可能的除零或无效数据情况

## 扩展性

该工具设计为高度可扩展，可以轻松添加新的计算字段类型和格式化选项。如需添加新的预定义字段，请在 `COMMON_CALCULATED_FIELDS` 中添加相应配置。
