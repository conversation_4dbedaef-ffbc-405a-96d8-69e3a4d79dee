/**
 * 图表tooltip工具函数
 */

/**
 * 计算字段配置接口
 */
export interface CalculatedField {
  /** 字段名称 */
  name: string
  /** 计算函数，接收所有系列的值，返回计算结果 */
  calculate: (values: Record<string, number>) => number
  /** 显示颜色 */
  color?: string
  /** 单位 */
  unit?: string
  /** 格式化函数 */
  formatter?: (value: number) => string
}

/**
 * Tooltip配置选项
 */
export interface TooltipOptions {
  /** 计算字段配置 */
  calculatedFields?: CalculatedField[]
  /** 是否显示分隔线 */
  showSeparator?: boolean
  /** 数值格式化函数 */
  valueFormatter?: (value: number) => string
  /** 自定义样式 */
  customStyle?: string
}

/**
 * 默认数值格式化函数
 */
const defaultValueFormatter = (value: number): string => {
  return value.toFixed(2)
}

/**
 * 创建增强的tooltip格式化函数
 * @param options 配置选项
 * @returns ECharts tooltip formatter函数
 */
export function createEnhancedTooltipFormatter(options: TooltipOptions = {}) {
  const {
    calculatedFields = [],
    showSeparator = true,
    valueFormatter = defaultValueFormatter,
    customStyle = '',
  } = options

  return (params: any) => {
    if (!Array.isArray(params) || params.length === 0) return ''

    const time = params[0].axisValue

    // 获取当前时间点的所有数据值
    const values: Record<string, number> = {}
    params.forEach((param: any) => {
      values[param.seriesName] = parseFloat(param.value) || 0
    })

    // 构建tooltip内容
    let tooltipContent = `<div style="font-size: 14px; line-height: 1.5; ${customStyle}">
      <div style="font-weight: bold; margin-bottom: 8px;">${time}</div>`

    // 显示原有数据系列
    params.forEach((param: any) => {
      const color = param.color
      const value = parseFloat(param.value) || 0
      const formattedValue = valueFormatter(value)
      tooltipContent += `
      <div style="margin: 4px 0; display: flex; align-items: center;">
        <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; margin-right: 8px; border-radius: 2px;"></span>
        <span style="min-width: 80px;">${param.seriesName}:</span>
        <span style="font-weight: bold; margin-left: 8px;">${formattedValue} MW</span>
      </div>`
    })

    // 添加计算字段
    if (calculatedFields.length > 0) {
      if (showSeparator) {
        tooltipContent += `<div style="border-top: 1px solid #eee; margin: 8px 0; padding-top: 8px;"></div>`
      }

      calculatedFields.forEach((field) => {
        const calculatedValue = field.calculate(values)
        if (!isNaN(calculatedValue)) {
          const color = field.color || '#666666'
          const unit = field.unit || 'MW'
          const formattedValue = field.formatter
            ? field.formatter(calculatedValue)
            : valueFormatter(calculatedValue)

          // 根据正负值设置颜色
          const valueColor = calculatedValue >= 0 ? '#27AE60' : '#E74C3C'

          tooltipContent += `
          <div style="margin: 4px 0; display: flex; align-items: center;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; margin-right: 8px; border-radius: 2px;"></span>
            <span style="min-width: 80px;">${field.name}:</span>
            <span style="font-weight: bold; margin-left: 8px; color: ${valueColor};">${formattedValue} ${unit}</span>
          </div>`
        }
      })
    }

    tooltipContent += '</div>'
    return tooltipContent
  }
}

/**
 * 创建断面图表专用的tooltip配置
 * 包含计划差值和市场差值的计算
 */
export function createSectionChartTooltipConfig(): TooltipOptions {
  return {
    calculatedFields: [
      {
        name: '计划差值',
        calculate: (values) => values['实时计划'] - values['实时潮流'],
        color: '#E74C3C',
      },
      {
        name: '市场差值',
        calculate: (values) => values['实时市场'] - values['实时潮流'],
        color: '#9B59B6',
      },
    ],
    showSeparator: true,
  }
}

/**
 * 预定义的常用计算字段
 */
export const COMMON_CALCULATED_FIELDS = {
  /** 计划差值：实时计划 - 实时潮流 */
  PLAN_DIFF: {
    name: '计划差值',
    calculate: (values: Record<string, number>) => values['实时计划'] - values['实时潮流'],
    color: '#E74C3C',
  },
  /** 市场差值：实时市场 - 实时潮流 */
  MARKET_DIFF: {
    name: '市场差值',
    calculate: (values: Record<string, number>) => values['实时市场'] - values['实时潮流'],
    color: '#9B59B6',
  },
  /** 计划市场差值：实时计划 - 实时市场 */
  PLAN_MARKET_DIFF: {
    name: '计划市场差值',
    calculate: (values: Record<string, number>) => values['实时计划'] - values['实时市场'],
    color: '#F39C12',
  },
} as const
