import { computed, ref } from 'vue'
import { usePeakShavingStore } from '@/stores'
import { PeakShavingService } from '@/utils/api/services/section-influence/peakShaving'
import type {
  SectionDataItem,
  SectionCurveDataPoint,
} from '@/utils/api/services/section-influence/peakShaving'
import { formatTime } from '@/utils/tools'
import type { SeriesData } from '@/types'
import {
  createEnhancedTooltipFormatter,
  createSectionChartTooltipConfig,
} from '@/utils/chart/tooltip'

export const useSectionChart = () => {
  const peakShavingStore = usePeakShavingStore()

  // 断面选择相关
  type SectionOption = { label: string; value: string }
  const sectionOptions = ref<SectionOption[]>([])
  const selectedSectionId = ref<string | null>(null)
  const loadingSections = ref(false)

  const selectedSectionName = computed(() => {
    const opt = sectionOptions.value.find((o) => o.value === selectedSectionId.value)
    return opt?.label || ''
  })

  // 图表相关
  const sectionChartRef = ref()
  const chartData = ref<SeriesData[]>([
    {
      name: '实时计划',
      color: '#3075F6',
      data: [],
    },
    {
      name: '实时市场',
      color: '#FFB637',
      data: [],
    },
    {
      name: '实时潮流',
      color: '#34C38F',
      data: [],
    },
    {
      name: '断面限额',
      color: '#999999',
      data: [],
    },
  ])
  const loadingChart = ref(false)

  // 图表配置
  const customConfig = {
    title: {
      top: 7,
    },
    grid: {
      top: '25%',
    },
    tooltip: {
      formatter: createEnhancedTooltipFormatter(createSectionChartTooltipConfig()),
    },
  }

  // 获取所有断面数据
  const fetchAllSections = async () => {
    try {
      loadingSections.value = true
      const res = await PeakShavingService.getAllSectionDataList()
      const list = res || []
      sectionOptions.value = list.map((item: SectionDataItem) => ({
        label: item.sectionName,
        value: item.sectionId,
      }))

      // 默认选择第一个断面
      if (sectionOptions.value.length > 0) {
        selectedSectionId.value = sectionOptions.value[0].value
        await fetchSectionCurveData()
      }
    } catch (error) {
      console.error('获取断面数据失败:', error)
    } finally {
      loadingSections.value = false
    }
  }

  // 获取断面曲线数据
  const fetchSectionCurveData = async () => {
    if (!selectedSectionId.value) return

    try {
      loadingChart.value = true
      const day = formatTime(peakShavingStore.time, 'YYYY-MM-DD')
      const res = await PeakShavingService.getSectionDataWithMarketAndPlan({
        sectionId: selectedSectionId.value,
        day: day,
      })

      updateChartData(res || [])
    } catch (error) {
      console.error('获取断面曲线数据失败:', error)
    } finally {
      loadingChart.value = false
    }
  }

  // 更新图表数据
  const updateChartData = (data: SectionCurveDataPoint[]) => {
    // 定义图表系列名称与数据属性的映射关系
    const seriesMapping: Record<string, keyof SectionCurveDataPoint> = {
      实时计划: 'planValue',
      实时市场: 'marketValue',
      实时潮流: 'value',
      断面限额: 'limit',
    }

    chartData.value.forEach((item) => {
      const dataKey = seriesMapping[item.name]
      if (dataKey) {
        item.data = data.map((d) => ({ name: d.time, value: d[dataKey] }))
      }
    })
  }

  // 选择指定断面
  const selectSectionById = (sectionId: string) => {
    console.log('🚀 ~ selectSectionById ~ sectionId:', sectionId)
    const option = sectionOptions.value.find((o) => o.value === sectionId)
    console.log('🚀 ~ selectSectionById ~ option:', option)
    if (option) {
      selectedSectionId.value = sectionId
      fetchSectionCurveData()
    }
  }

  return {
    sectionOptions,
    selectedSectionId,
    selectedSectionName,
    loadingSections,
    sectionChartRef,
    chartData,
    loadingChart,
    customConfig,
    fetchAllSections,
    fetchSectionCurveData,
    selectSectionById,
  }
}
